# 1. 术语、节点类型与不变量

**基础模型**

* 流程用 `ProcessNode` 表示，包含 `id`、`startEventId`、`flowNodeMap<String, BaseNode>`、`child` 等（用于子流程标识）。序列化使用 `toJson()`；新增节点的 ID 采用 `UUID` 生成（你代码已引入相关依赖，可直接使用）。
* 可路由节点实现 `IRoutable`：必须维护 `prveId`（上一个）与 `nextId`（下一个）。`hasValidRoute()` 的语义是 `nextId` 非空或为 `"99"`（连接结束）。
* 分支网关实现 `IHasBranches`：维护分支根节点 ID 列表 `flowIds`。
* 条件分支/唯一分支使用 `IHasConditions`：维护 `operateCondition`。
* 子流程能力通过 `IHasSubProcess`：包含嵌套的 `ProcessNode`。

**典型类型（来自示例）**

* `typeId=0`：开始事件；`typeId=1`：网关“分支”；`typeId=2`：分支叶子（承载条件并指向该分支首个普通节点）；`typeId=26`：子流程入口；`typeId=27/5/4/...`：普通业务节点等。见示例数据结构。
* 网关节点示例（`typeId=1`）含 `flowIds` 与 `gatewayType`（值 1=并行，2=唯一）：`65e9874f57c8d64b906944b4`。
* 分支叶子（`typeId=2`）承载条件 `operateCondition`，其 `nextId` 指向该分支第一条“普通链”的首节点；分支尾部“最后一个普通节点”的 `nextId=""` 表示分支到此终止，等待网关汇合。
* 结束连接：**（规则）** 网关节点、普通节点（但**网关里的叶子节点除外**）`nextId="99"` 表示接到结束；分支尾部普通节点用 `nextId=""` 表示“无下个节点（等汇合）”。**每个流程内最多出现一次 `"99"`**，主流程与每个子流程各自独立计数。你给的示例中：主流程有一个 `nextId="99"` 的普通节点（如“通知下级部门”），子流程里也有一个审批节点 `nextId="99"`。 

> **不变量汇总**
>
> 1. `startEventId` 唯一；2) 每个（子）流程内**至多一个** `nextId="99"`；3) 分支叶子的分支链尾 `nextId=""`；4) `IRoutable` 的 `prveId/nextId` 必须成对维护；5) `IHasBranches.flowIds` 与 `flowNodeMap` 一致；6) 修改网关类型/分支顺序后，条件与连线仍需满足 `IHasConditions.hasValidConditions()` 与 `IRoutable.hasValidRoute()`。 

---

# 2. 遍历模型（非 DAG，线性+分支递归）

实现一个**遍历器**，从 `startEventId` 开始，按 `IRoutable.nextId` 前进；遇到网关（`typeId=1`）时：

* 读取 `IHasBranches.flowIds`，对每个分支叶子（`typeId=2`）：

  * 先访问叶子（读取/维护 `IHasConditions`），再从其 `nextId` 进入分支链，直至某个普通节点 `nextId=""` 处停止；
* 分支都遍历完后，从网关的 `nextId` 继续主链（若为 `"99"` 则结束）。

> 整体是“主链 while + 分支 DFS”，不依赖 DAG 拓扑。示例结构与字段分布可直接印证该遍历方式。

---

# 3. 公共工具与校验

实现以下**原子工具函数**（后续 CRUD 全部复用）：

1. **取/判工具**

* `get(nodeId)`：从 `flowNodeMap` 取节点；
* `isGateway(node)` = `typeId==1`；`isBranchLeaf(node)` = `typeId==2`；`isEnd(nextId)` = `"99"`；`isSubProcess(node)` = 存在 `processNode`； 

2. **上下文定位**

* `prevOf(node)` = `IRoutable.prveId`；`nextOf(node)` = `IRoutable.nextId`。网关的“后继”即其 `nextId`；分支尾的“后继”为空串。

3. **上下文自动连线策略（贯穿所有 CRUD）**

* **断开**：`disconnect(prev,node,next)`

  * 若 `prev` 存在：`prev.nextId = (next==null? "" : next.id 或 "99")`（分支内尾部要置 `""`，主链可直连或置 `"99"`）；
  * 若 `next` 存在：`next.prveId = (prev==null? "" : prev.id)`；
* **连接**：`connect(prev,node,next)`

  * 维护 `prev.nextId = node.id`、`node.prveId = prev.id`、`node.nextId = (next==null? "" : next.id)`、`next.prveId = node.id`；
* **替换**：`replace(old, neo)`

  * `disconnect(prev, old, next)` + `connect(prev, neo, next)`；
* **注意**：若涉及 `"99"`，调用 `ensureSingleEnd(process)`（见下）。

4. **结束唯一性**

* `ensureSingleEnd(process)`：扫描本 `ProcessNode.flowNodeMap`，统计 `nextId="99"` 的**指针数**；

  * 若 >1：将除**一个**之外的 `"99"` 改为与“真正结束点”之前的一个“连接点”相连（推荐：把旧 `"99"` 所在节点改连至“结束前的最后节点”，或直接断为 `""` 并通过后续结构（如网关）汇合，从而只保留一个 `"99"`）；
  * 在**子流程**编辑时，仅扫描子流程的 `processNode`，互不干扰。

> 这样可满足“主/子流程各自仅有一个 99”的硬性规则。示例中主流程与子流程各见一处 `99`。

5. **校验**

* `IRoutable.hasValidRoute()` 保证主链/普通节点的路由有效；分支尾允许 `nextId=""`；
* `IHasBranches.hasValidBranches()` 确保 `flowIds` 非空且每个 ID 合法；
* `IHasConditions.hasValidConditions()` 用于唯一分支时的条件合法性检查；
* `IHasSubProcess.hasValidSubProcess()` 用于子流程结构有效性；

---

# 4. CRUD 设计

## 4.1 网关操作

### A) 新增网关（默认并行分支：`gatewayType=1`）

**输入**：插入位置节点 `anchor`（在其后插入）、`mode ∈ {leftSide, noMove}`、`gatewayType ∈ {1,2}`（默认 1）
**步骤**：

1. 生成 `gateway`（`typeId=1`，实现 `IHasBranches`，`gatewayType` 置入）、两条分支叶子 `leafL/leafR`（`typeId=2`，实现 `IHasConditions`，默认空条件或兜底条件）。
2. **断开** `anchor -> next`，改为 `anchor -> gateway`；`gateway.nextId = next.id`（或 `"99"`，见下“leftSide”）；维护 `prveId`。
3. `gateway.flowIds = [leafL.id, leafR.id]`；`leafL.prveId = leafR.prveId = gateway.id`。
4. `mode=noMove`（常规）：

  * 不移动 `next` 及其后续；两条分支的**链**均为空：`leafL.nextId = leafR.nextId = ""`；
  * **网关汇合后**继续执行 `next`（`gateway.nextId = next.id`）。
5. `mode=leftSide`（左侧放置）：

  * **把 `anchor` 下方原有链条整体搬到左分支**：`leafL.nextId = next.id`，并把该链条最后一个普通节点的 `nextId` 改为 `""`（表示分支尾等待汇合）；
  * **“两条默认分支都直接连接到结束”**的语义**按规则 6 处理**：为**避免分支内部使用 `99`**（规则明确“分支叶子除外”、且每流程仅能有一个 `99`），**建议实现为**：保留两分支尾 `""`，将**网关本身**的 `nextId` 指向 `"99"`。这样语义等价为“经过网关后流程终止”，且满足“99 唯一”“分支尾不用 99”。若本流程已存在 `99`，执行 `ensureSingleEnd()` 做收敛（保留新的或旧的一个）。

> 示例中的网关、分支叶子与分支链尾的“空 nextId”形态可直接参考。

**副作用与校验**：

* `IHasBranches.hasValidBranches()`、`IRoutable.hasValidRoute()`、`IHasConditions`（若唯一分支则需要有效条件）。 

### B) 删除网关

**输入**：`gateway`
**分两种**：

* **普通删除**（仍有 ≥2 分支）：

  * 需先检查是否允许删除（可能有外部引用/子流程耦合等）；
  * 清理其 `flowIds` 引用（可连同分支叶子与分支内部链做“批量删除”，或做“扁平化归并”，由产品需求定）；
  * 主链层面：把 `gateway.prveId` 直接连到 `gateway.nextId`（或 `"99"`），并更新两端 `prveId/nextId`。
* **符合 3.1.2 的特殊删除**（当网关**只剩最后一条分支**时）：

  1. 取剩余分支叶子 `leaf`，找到其分支链 `leaf -> ... -> tail( nextId="" )`；
  2. **彻底删除网关与条件分支（叶子）**；
  3. 将该分支链整体视为**普通直链**：把 `gateway.prveId` 与分支首节点连上；分支尾 `tail.nextId` 由 `""` 改为原 `gateway.nextId`（或 `"99"`）；
  4. 维护所有涉及节点的 `prveId/nextId`；
  5. 运行 `ensureSingleEnd()` 确保结束唯一性。

> 这与“将最后一条分支节点作为普通节点进行上下文自动连接”的描述一致。

### C) 修改网关类型（并行↔唯一）

* 修改 `gateway.gatewayType`；
* 若改为**唯一分支（2）**：必须检查每个分支叶子 `operateCondition` 的有效性（否则需创建默认条件或报错）。
* 若改为**并行分支（1）**：可清空条件或保留（仅作展示，不参与流转判定）。
* **副作用**：若之前唯一分支存在多个“能命中”的条件组合，需要在保存时进行冲突检测（不落地执行，纯结构层面提示即可）。

---

## 4.2 分支操作（针对网关）

### A) 增加分支

* 在 `gateway.flowIds` 中新增一个分支叶子 `leafNew`（`typeId=2`），`leafNew.prveId=gateway.id`，`leafNew.nextId=""`；
* 若是唯一分支，需为 `leafNew` 初始化默认条件或复制某分支条件后做唯一化；
* 校验 `flowIds` 与条件合法性。

### B) 删除分支

* 从 `gateway.flowIds` 移除该分支 ID，并**连带删除**此叶子及其分支链（或根据产品设定将链条提升到主链）；
* 若删后只剩一条分支，触发**3.1.2 特殊逻辑**：同时删除网关并把最后分支链并入主链（见 4.1.B）。

### C) 调整分支顺序

* 对 `gateway.flowIds` 做排序变更（稳定排序）；
* 唯一分支时，UI/表达式按新顺序评估条件，结构层不需再改动；
* 校验 `flowIds` 非空且每项有效。

### D) 复制分支

* 复制分支叶子与其**分支链**：

  * 为所有复制出的节点生成**新 ID**（UUID），写入 `flowNodeMap`；
  * 更新链上 `prveId/nextId` 指向新的 ID；
  * 复制条件 `operateCondition`；若唯一分支需**去重/修正**避免条件重叠导致冲突；
* 将复制出的分支叶子 ID 插入 `gateway.flowIds`（相邻位置）。

---

## 4.3 普通节点操作

### A) 增加

* **主链插入**：给定 `anchor`，在其后插入 `nodeNew`（`IRoutable`）。

  * `disconnect(anchor, next)` → `connect(anchor, nodeNew, next)`；
  * 若 `next` 为 `"99"`，则 `nodeNew.nextId="99"`，并调用 `ensureSingleEnd()`（保持唯一）。
* **分支链插入**：在某分支链上插入，规则同上，但**尾节点**允许 `nextId=""`。

### B) 删除

* 定位 `node` 的 `prev/next`，执行 `disconnect(prev, node, next)` 并连回 `prev->next`；
* 若删除造成网关只剩一条分支，转入 3.1.2 逻辑；
* 若涉及 `"99"`，再跑 `ensureSingleEnd()`。

### C) 修改

* 变更节点属性（如名称、抄送人等）不影响连线；
* 变更 `nextId/prveId`（手动改线）必须走“上下文自动连线”工具以保持一致性。

---

# 5. 子流程与多层结构

* 子流程节点（`typeId=26`）实现 `IHasSubProcess`，其内部 `processNode` 是**独立的流程**：

  * 其 `startEventId/flowNodeMap` 完整且自洽；
  * **独立遵守“99 唯一”**，与主流程互不干扰；
  * 对子流程做任何 CRUD，与主流程同样走“遍历 + 自动连线 + 唯一结束校验”。 

---

# 6. 稳定性策略（结合示例结构验证）

* **主链→网关→分支→汇合→后继**的连线形态，应与示例一致：

  * 网关 `65e9874f...b4` 的 `flowIds` 指向两条分支叶子 `...b5 / ...b6`，叶子携带条件并指向各自分支首节点；分支链尾的普通节点 `nextId=""`；汇合后根据网关 `nextId` 进入后继或结束。
  * 主流程末端的普通节点（如“通知下级部门”）直接 `nextId="99"` 结束；子流程里也存在一个审批节点 `nextId="99"`。
* **修改/删除**时，始终走“上下文自动连线”三步（断开/连接/替换）并在必要时执行 `ensureSingleEnd()`，可避免“悬空指针”“多 99”“分支断链”等问题。
* **唯一分支**切换或条件变更后，调用 `IHasConditions.hasValidConditions()` 做保存前检查。

---

# 7. 约束冲突与策略优先级（关键边界情况）

1. **leftSide 模式且本流程已存在 `"99"`**：

  * 优先将**网关的 `nextId="99"`**作为新的“唯一结束指针”；对旧的 `"99"` 位置执行**回退**（改连回主链或置 `""` 并通过网关汇合），由 `ensureSingleEnd()` 统一收敛。
2. **删除导致网关只剩一支**：

  * 触发 3.1.2“网关退化”逻辑，把最后分支链提升为普通直链。
3. **分支复制（唯一分支）**：

  * 复制后必须对 `operateCondition` 做去重约束，避免结构上“多条都可命中”的不一致。
4. **子流程编辑**：

  * 对子流程运行同一套规则，但**不可**影响主流程的 `"99"`；保存时对子流程单独校验。

---

# 8. 性能与事务性

* 所有操作均是 **O(N)** 量级遍历（`N` 为受影响节点数），满足“非 DAG”+ 遍历式编辑的要求。
* 每次复合操作（例如“删除网关并并入主链”）**原子化**：在内存完成所有连线与校验后，一次性写回 `flowNodeMap` 并 `toJson()` 序列化输出（或持久化）。

---

# 9. 验收清单（建议用例）

1. **新增网关（noMove）**：插在“工作表事件触发”之后，`gateway.nextId` 指向原“未命名审批流程”，两分支尾为 `""`。
2. **新增网关（leftSide）**：同上，但将“未命名审批流程→…→通知下级部门(99)”整条链移到左分支，分支尾 `""`，**网关本身** `nextId="99"`，并验证只存在一个 `"99"`。
3. **删除网关至只剩一分支**：触发 3.1.2，把该分支链并回主链；验证 `IRoutable` 与 `flowIds`、`operateCondition` 均一致。
4. **分支复制/排序**：确认 `flowIds` 顺序变化、条件正确复制与去重。
5. **子流程中新增节点并结束**：只在子流程内出现一个 `"99"`，主流程不受影响。
